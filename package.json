{"name": "shredstream-decoder", "version": "0.0.0", "main": "index.js", "types": "index.d.ts", "napi": {"name": "shredstream-decoder", "triples": {"additional": ["aarch64-apple-darwin"]}}, "license": "MIT", "devDependencies": {"@napi-rs/cli": "^2.18.4", "ava": "^6.0.1"}, "ava": {"timeout": "3m"}, "engines": {"node": ">= 10"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --platform --release", "build:debug": "napi build --platform", "prepublishOnly": "napi prepublish -t npm", "test": "ava", "universal": "napi universal", "version": "napi version"}, "packageManager": "yarn@4.9.1"}